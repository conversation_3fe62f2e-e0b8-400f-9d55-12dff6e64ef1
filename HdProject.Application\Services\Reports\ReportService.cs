using System.Globalization;
using HdProject.Application.Services.Interfaces.Reports;
using HdProject.Domain.Context.Reports;
using SqlSugar;
using ClosedXML.Excel;

namespace HdProject.Application.Services.Reports
{
    public class ReportService : IReportService
    {
        private readonly IBankSummaryDal _dal;

        public ReportService(IBankSummaryDal dal)
        {
            _dal = dal;
        }

        public async Task<BankSummaryResponseDto> GenerateBankSummaryReportAsync(BankSummaryRequestDto request)
        {
            // 1) 解析日期
            if (!DateTime.TryParseExact(request.StartDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var startDate))
                throw new ArgumentException("StartDate 格式错误，应为 yyyy-MM-dd");
            if (!DateTime.TryParseExact(request.EndDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var endDate))
                throw new ArgumentException("EndDate 格式错误，应为 yyyy-MM-dd");

            // 2) 执行 SQL（扁平结果）
            var flat = await _dal.GetFlatAsync(startDate, endDate, request.BankSKs);

            // 3) 塑形为响应结构
            return Pivot(flat);
        }

        public async Task<(byte[] Bytes, string FileName, string ContentType)> ExportBankSummaryXlsxAsync(BankSummaryRequestDto request, string? titleDateRange = null)
        {
            var data = await GenerateBankSummaryReportAsync(request);

            using var wb = new XLWorkbook();
            var ws = wb.Worksheets.Add("银行汇总表");

            int col = 1;
            int row = 1;

            // 标题（格式：yyyy年M月d日），不展示“银行：”
            if (!DateTime.TryParseExact(request.StartDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var start))
                throw new ArgumentException("StartDate 格式错误，应为 yyyy-MM-dd");
            if (!DateTime.TryParseExact(request.EndDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var end))
                throw new ArgumentException("EndDate 格式错误，应为 yyyy-MM-dd");
            var startZh = start.ToString("yyyy年M月d日");
            var endZh = end.ToString("yyyy年M月d日");

            ws.Cell(row, col).Value = $"日期：{startZh} 至 {endZh}";
            ws.Range(row, col, row, 1 + data.ColumnHeaders.Count * 5 + 4).Merge().Style
                .Font.SetBold().Font.SetFontSize(12);
            row += 2;

            // 叠表头 第一行
            ws.Cell(row, col).Value = "银行券名称/门店";
            ws.Range(row, col, row + 1, col).Merge().Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center)
              .Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            col++;
            foreach (var shop in data.ColumnHeaders)
            {
                ws.Cell(row, col).Value = shop.ShopName;
                // 每店 5 列：核销量、核销额、补贴额、服务费、合计(核销额+补贴额+服务费)
                ws.Range(row, col, row, col + 4).Merge().Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                ws.Cell(row + 1, col).Value = "核销量";
                ws.Cell(row + 1, col + 1).Value = "核销额";
                ws.Cell(row + 1, col + 2).Value = "补贴额";
                ws.Cell(row + 1, col + 3).Value = "服务费";
                ws.Cell(row + 1, col + 4).Value = "合计";
                col += 5;
            }

            // 尾部汇总四列
            ws.Cell(row, col).Value = "合计核销额";
            ws.Cell(row, col + 1).Value = "补贴金额";
            ws.Cell(row, col + 2).Value = "平台服务费";
            ws.Cell(row, col + 3).Value = "合计实收金额";
            ws.Range(row, col, row, col + 3).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            row += 2;

            // 数据行
            foreach (var r in data.Rows)
            {
                col = 1;
                ws.Cell(row, col++).Value = r.DealName;
                foreach (var shop in data.ColumnHeaders)
                {
                    r.ShopData.TryGetValue(shop.ShopID, out var cell);
                    var cnt = cell?.Count ?? 0;
                    var amt = cell?.Amount ?? 0m;
                    var sub = cell?.Subsidy ?? 0m;
                    var fee = cell?.PlatformFee ?? 0m;
                    ws.Cell(row, col++).Value = cnt;
                    ws.Cell(row, col++).Value = amt;
                    ws.Cell(row, col++).Value = sub;
                    ws.Cell(row, col++).Value = fee;
                    ws.Cell(row, col++).Value = amt + sub + fee;
                }
                // 行尾合计：使用 RowTotal 中的 Amount 和 Subsidy
                var rowAmtSum = r.RowTotal.Amount;
                var rowSubSum = r.RowTotal.Subsidy;
                var rowFeeSum = r.RowTotal.PlatformFee;
                var rowNetSum = r.RowTotal.NetAmount;
                ws.Cell(row, col++).Value = rowAmtSum;
                ws.Cell(row, col++).Value = rowSubSum;
                ws.Cell(row, col++).Value = rowFeeSum;
                ws.Cell(row, col++).Value = rowNetSum;
                row++;
            }

            // 合计行
            col = 1;
            ws.Cell(row, col).Value = "合计";
            ws.Range(row, col, row, col).Style.Font.SetBold();
            col++;
            foreach (var shop in data.ColumnHeaders)
            {
                var cnt = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Count : 0);
                var amt = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Amount : 0m);
                var sub = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Subsidy : 0m);
                var fee = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.PlatformFee : 0m);
                var net = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.NetAmount : 0m);
                ws.Cell(row, col++).Value = cnt;
                ws.Cell(row, col++).Value = amt;
                ws.Cell(row, col++).Value = sub;
                ws.Cell(row, col++).Value = fee;
                ws.Cell(row, col++).Value = net;
            }
            ws.Cell(row, col++).Value = data.GrandTotal.TotalAmount;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalSubsidy;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalPlatformFee;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalNetAmount;

            // 样式
            var lastCol = 1 + data.ColumnHeaders.Count * 5 + 4;
            var lastRow = row;
            ws.Range(3, 1, lastRow, lastCol).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            ws.Range(3, 1, lastRow, lastCol).Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            ws.Columns().AdjustToContents();

            using var ms = new MemoryStream();
            wb.SaveAs(ms);
            var bytes = ms.ToArray();
            var fileName = $"银行汇总表_{request.StartDate}_to_{request.EndDate}.xlsx";
            return (bytes, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        private static BankSummaryResponseDto Pivot(List<FlatReportDataDto> flat)
        {
            var resp = new BankSummaryResponseDto();

            // 列头（去重的门店）
            var shops = flat
                .GroupBy(x => new { x.ShopID, x.ShopName })
                .Select(g => new ShopHeaderDto { ShopID = g.Key.ShopID, ShopName = g.Key.ShopName })
                .OrderBy(s => s.ShopName)
                .ToList();
            resp.ColumnHeaders = shops;

            // 行（券）
            var byDeal = flat.GroupBy(x => new { x.DealSK, x.DealName })
                .OrderBy(g => g.Key.DealName);

            foreach (var dealGroup in byDeal)
            {
                var row = new BankSummaryRowDto
                {
                    DealSK = dealGroup.Key.DealSK,
                    DealName = dealGroup.Key.DealName,
                    ShopData = new Dictionary<int, ShopCellDto>()
                };

                int rowCount = 0;
                decimal rowAmount = 0m, rowSubsidy = 0m, rowPlatformFee = 0m, rowNetAmount = 0m;

                // 为所有列头填充
                foreach (var shop in shops)
                {
                    var cellAgg = dealGroup.Where(x => x.ShopID == shop.ShopID);
                    var cell = new ShopCellDto
                    {
                        Count = cellAgg.Sum(x => x.TotalCount),
                        Amount = cellAgg.Sum(x => x.TotalAmount),
                        Subsidy = cellAgg.Sum(x => x.TotalSubsidy),
                        PlatformFee = cellAgg.Sum(x => x.TotalPlatformFee),
                        NetAmount = cellAgg.Sum(x => x.TotalNetAmount)
                    };
                    row.ShopData[shop.ShopID] = cell;

                    rowCount += cell.Count;
                    rowAmount += cell.Amount;
                    rowSubsidy += cell.Subsidy;
                    rowPlatformFee += cell.PlatformFee;
                    rowNetAmount += cell.NetAmount;
                }

                row.RowTotal = new ShopTotalsDto { Count = rowCount, Amount = rowAmount, Subsidy = rowSubsidy, PlatformFee = rowPlatformFee, NetAmount = rowNetAmount };
                resp.Rows.Add(row);
            }

            // 总计
            resp.GrandTotal = new BankSummaryGrandTotalDto
            {
                TotalAmount = flat.Sum(x => x.TotalAmount),
                TotalSubsidy = flat.Sum(x => x.TotalSubsidy),
                TotalPlatformFee = flat.Sum(x => x.TotalPlatformFee),
                TotalNetAmount = flat.Sum(x => x.TotalNetAmount)
            };

            return resp;
        }
    }
}
