﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositories.Imp;
using SqlSugar;

namespace HdProject.Infrastructure.Repositorys.Imp
{
    public class RepositoryOperateData<T> : Repository<T>, IRepositoryOperateData<T>
       where T : class, new()
    {
        public RepositoryOperateData(ISqlSugarClient db) : base(db)
        {
            base.ConfigId = "OperateData";
            // 连接的结构（InitKeyType）由 SqlSugarDbContext 初始化时统一设置。
        }


    }
   
}
