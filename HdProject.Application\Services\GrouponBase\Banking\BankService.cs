using HdProject.Application.Services.Interfaces.GrouponBase.Banking;
using HdProject.Common.DTOs.Banking;
using HdProject.Domain.Entities.GroupBase;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using System.Linq.Expressions;

namespace HdProject.Application.Services.GrouponBase.Banking
{
    public class BankService : IBankService
    {
    private readonly IRepositoryOperateData<Dim_Bank> _repo;

    public BankService(IRepositoryOperateData<Dim_Bank> repo)
        {
            _repo = repo;
        }

        public async Task<int> CreateAsync(BankCreateDto dto)
        {
            // 由数据库自增生成 BankSK
            var entity = new Dim_Bank { BankName = dto.BankName };
            return await _repo.InsertAsync(entity);
        }

        public async Task<int> DeleteAsync(int id)
        {
            return await _repo.DeleteAsync(id);
        }

        public async Task<Dim_Bank?> GetByIdAsync(int id)
        {
            return await _repo.GetByIdAsync(id);
        }

        public async Task<List<Dim_Bank>> GetListAsync(BankQueryDto query)
        {
            var page = query as Pagination;
            if (string.IsNullOrWhiteSpace(query.Keyword))
            {
                return await _repo.GetPageListAsync(page);
            }
            else
            {
                return await _repo.GetPageListAsync(page, b => b.BankName!.Contains(query.Keyword));
            }
        }

        public async Task<int> UpdateAsync(int id, BankUpdateDto dto)
        {
            var entity = await _repo.GetByIdAsync(id);
            if (entity == null) return 0;
            entity.BankName = dto.BankName;
            return await _repo.UpdateAsync(entity);
        }

        public async Task<int> CountAsync(string? keyword)
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return await _repo.CountAsync();
            }
            else
            {
                return await _repo.CountAsync(b => b.BankName!.Contains(keyword));
            }
        }
    }

    public class BankDealService : IBankDealService
    {
    private readonly IRepositoryOperateData<Dim_Bank_Deal> _repo;

    public BankDealService(IRepositoryOperateData<Dim_Bank_Deal> repo)
        {
            _repo = repo;
        }

        public async Task<int> CreateAsync(BankDealCreateDto dto)
        {
            var entity = new Dim_Bank_Deal
            {
                BankSK = dto.BankSK,
                FdNo = dto.FdNo,
                DealName = dto.DealName,
                DealAmount = dto.DealAmount,
                SubsidyAmount = dto.SubsidyAmount,
                TotalAmount = dto.TotalAmount,
                ServiceFee = dto.ServiceFee,
                NetAmount = dto.NetAmount
            };
            return await _repo.InsertAsync(entity);
        }

        public async Task<int> DeleteAsync(int id)
        {
            return await _repo.DeleteAsync(id);
        }

        public async Task<Dim_Bank_Deal?> GetByIdAsync(int id)
        {
            return await _repo.GetByIdAsync(id);
        }

        public async Task<List<Dim_Bank_Deal>> GetListAsync(BankDealQueryDto query)
        {
            var page = query as Pagination;
            
            // 简化查询，先测试基本的查询是否工作
            if (query.BankSKs == null || query.BankSKs.Length == 0)
            {
                // 没有银行SK过滤，使用原始查询
                return await _repo.GetPageListAsync(page, d =>
                    (string.IsNullOrEmpty(query.FdNo) || d.FdNo.Contains(query.FdNo)) &&
                    (string.IsNullOrEmpty(query.DealName) || d.DealName.Contains(query.DealName))
                );
            }
            else
            {
                // 有银行SK过滤，使用简化的Contains方式
                var bankSks = query.BankSKs;
                return await _repo.GetPageListAsync(page, d =>
                    bankSks.Contains(d.BankSK) &&
                    (string.IsNullOrEmpty(query.FdNo) || d.FdNo.Contains(query.FdNo)) &&
                    (string.IsNullOrEmpty(query.DealName) || d.DealName.Contains(query.DealName))
                );
            }
        }

        public async Task<int> UpdateAsync(int id, BankDealUpdateDto dto)
        {
            var entity = await _repo.GetByIdAsync(id);
            if (entity == null) return 0;
            entity.BankSK = dto.BankSK;
            entity.FdNo = dto.FdNo;
            entity.DealName = dto.DealName;
            entity.DealAmount = dto.DealAmount;
            entity.SubsidyAmount = dto.SubsidyAmount;
            entity.TotalAmount = dto.TotalAmount;
            entity.ServiceFee = dto.ServiceFee;
            entity.NetAmount = dto.NetAmount;
            return await _repo.UpdateAsync(entity);
        }

        public async Task<int> CountAsync(int[]? bankSks, string? fdNo, string? dealName)
        {
            // 简化查询，先测试基本的查询是否工作
            if (bankSks == null || bankSks.Length == 0)
            {
                // 没有银行SK过滤，使用原始查询
                return await _repo.CountAsync(d =>
                    (string.IsNullOrEmpty(fdNo) || d.FdNo.Contains(fdNo)) &&
                    (string.IsNullOrEmpty(dealName) || d.DealName.Contains(dealName))
                );
            }
            else
            {
                // 有银行SK过滤，使用简化的Contains方式
                return await _repo.CountAsync(d =>
                    bankSks.Contains(d.BankSK) &&
                    (string.IsNullOrEmpty(fdNo) || d.FdNo.Contains(fdNo)) &&
                    (string.IsNullOrEmpty(dealName) || d.DealName.Contains(dealName))
                );
            }
        }
    }
}
